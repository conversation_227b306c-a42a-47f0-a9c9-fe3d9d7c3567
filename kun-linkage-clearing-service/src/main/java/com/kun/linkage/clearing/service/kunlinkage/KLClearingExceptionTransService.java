package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.clearing.facade.vo.boss.ClearingExceptionInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.boss.ClearingExceptionInquiryRequestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLClearingExceptionTrans;
import com.kun.linkage.common.db.mapper.ClearingExceptionTransMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KLClearingExceptionTransService {

    @Resource
    private ClearingExceptionTransMapper clearingExceptionTransMapper;

    public PageResult<ClearingExceptionInquiryPageVO> pageList(ClearingExceptionInquiryRequestVO requestVO) {
        LambdaQueryWrapper<KLClearingExceptionTrans> wrapper = Wrappers.lambdaQuery();
        wrapper.ge(KLClearingExceptionTrans::getClearingDate, requestVO.getClearingDateFrom().replaceAll("-", ""));
        wrapper.le(KLClearingExceptionTrans::getClearingDate, requestVO.getClearingDateUntil().replaceAll("-", ""));
        if (requestVO.getClearingId() != null) {
            wrapper.eq(KLClearingExceptionTrans::getClearingId, requestVO.getClearingId());
        }
        if (requestVO.getProcessor() != null) {
            wrapper.eq(KLClearingExceptionTrans::getProcessor, requestVO.getProcessor());
        }
        if (requestVO.getAuthorizationDateFrom() != null) {
            wrapper.ge(KLClearingExceptionTrans::getTransTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateFrom(), DateUtils.DAY_PATTERN)));
        }
        if (requestVO.getAuthorizationDateUntil() != null) {
            wrapper.le(KLClearingExceptionTrans::getTransTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateFrom(), DateUtils.DAY_PATTERN)));
        }
        if (requestVO.getMerchantNo() != null) {
            wrapper.eq(KLClearingExceptionTrans::getMerchantNo, requestVO.getMerchantNo());
        }
        if (requestVO.getTransType() != null) {
            wrapper.eq(KLClearingExceptionTrans::getTransType, requestVO.getTransType());
        }
        if (requestVO.getAcquireReferenceNo() != null) {
            wrapper.eq(KLClearingExceptionTrans::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (requestVO.getArn() != null) {
            wrapper.eq(KLClearingExceptionTrans::getArn, requestVO.getArn());
        }
        if (requestVO.getGatewayCardId() != null) {
            wrapper.eq(KLClearingExceptionTrans::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (requestVO.getApproveCode() != null) {
            wrapper.eq(KLClearingExceptionTrans::getApproveCode, requestVO.getApproveCode());
        }
        if (requestVO.getProcessFlag() != null) {
            wrapper.eq(KLClearingExceptionTrans::getStatus, requestVO.getProcessFlag());
        }
        if (requestVO.getCardNoSuffix() != null) {
            wrapper.eq(KLClearingExceptionTrans::getMaskedCardNo, requestVO.getCardNoSuffix());
        }
        wrapper.orderByDesc(KLClearingExceptionTrans::getCreateTime);
        PageResult<KLClearingExceptionTrans> pageResult =
            PageHelperUtil.getPage(requestVO, () -> clearingExceptionTransMapper.selectList(wrapper));
        PageResult<ClearingExceptionInquiryPageVO> result = convertToPageResult(pageResult);
        return result;
    }

    private PageResult<ClearingExceptionInquiryPageVO> convertToPageResult(
        PageResult<KLClearingExceptionTrans> pageResult) {
        List<ClearingExceptionInquiryPageVO> list = pageResult.getData().stream().map(clearingTrans -> {
            ClearingExceptionInquiryPageVO vo = new ClearingExceptionInquiryPageVO();
            vo.setClearingId(clearingTrans.getClearingId());
            vo.setProcessor(clearingTrans.getProcessor());
            vo.setClearingDate(DateUtils.formatDate(clearingTrans.getCreateTime(), DateUtils.DAY_PATTERN));
            if (clearingTrans.getTransTime() != null) {
                vo.setAuthorizationTime(DateUtils.formatDate(clearingTrans.getTransTime(), DateUtils.DATETIME_PATTERN));
            }
            vo.setMerchantNo(clearingTrans.getMerchantNo());
            vo.setMerchantName(clearingTrans.getMerchantName());
            vo.setGatewayCardId(clearingTrans.getGatewayCardId());
            vo.setTransType(clearingTrans.getTransType());
            vo.setTransCurrency(clearingTrans.getTransCurrency());
            vo.setTransCurrencyExponent(clearingTrans.getTransCurrencyExponent());
            vo.setTransAmount(clearingTrans.getTransAmount());
            vo.setCardholderCurrency(clearingTrans.getCardholderBillingCurrency());
            vo.setCardholderCurrencyExponent(clearingTrans.getCardholderCurrencyExponent());
            vo.setCardholderAmount(clearingTrans.getCardholderBillingAmount());
            vo.setCardholderMarkupAmount(clearingTrans.getCardholderMarkupBillingAmount());
            vo.setProcessBy(clearingTrans.getProcessBy());
            vo.setAcquireReferenceNo(clearingTrans.getAcquireReferenceNo());
            vo.setApproveCode(clearingTrans.getApproveCode());
            vo.setArn(clearingTrans.getArn());
            vo.setAuthTransId(clearingTrans.getAuthFlowId());
            vo.setExceptionReason(clearingTrans.getExceptionReason());
            vo.setProcessFlag(clearingTrans.getProcessFlag());
            vo.setOriginalClearingId(clearingTrans.getOriginalProcessorTransId());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<ClearingExceptionInquiryPageVO>(list, pageResult.getPageNum(), pageResult.getPageSize(),
            pageResult.getTotal(), pageResult.getExtraInfo());
    }

    public Result<Boolean> assignToMerchant(String clearingId) {
        // TODO: Implement the logic to assign the exception transaction to the merchant
        return Result.success(true);
    }

    public Result<Boolean> retry(String clearingId) {
        // TODO: Implement the logic to retry the exception transaction
        return Result.success(true);
    }
}
