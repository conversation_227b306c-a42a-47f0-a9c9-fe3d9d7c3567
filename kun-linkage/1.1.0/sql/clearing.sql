INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '1050', '消费', 'Sales', 1, 1, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '1051', '汇款', 'Transfer out', 1, 2, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '1060', '退货', 'Refund', 1, 3, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '1061', '收款', 'Transfer In', 1, 4, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '1070', '取现', 'Withdrawal', 1, 5, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '2050', '消费撤销', 'Void sales', 1, 6, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '2051', '汇款撤销', 'Void transfer out', 1, 7, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '2060', '退货撤销', 'Void refund', 1, 8, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '2061', '收款撤销', 'Void transfer In', 1, 9, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '2070', '取现撤销', 'Void withdrawal', 1, 10, now(), NULL, now(), NULL);
INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KL_CLEARING_TRANS', 'CLEARING_TYPE', '3000', '争议', 'Dispute', 1, 11, now(), NULL, now(), NULL);


ALTER TABLE `kc_clearing_info_2025_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;


ALTER TABLE `kc_clearing_info_2025_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2025_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2025_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2026_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2026_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2026_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2026_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2027_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2027_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2027_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2027_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2028_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2028_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2028_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2028_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2029_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2029_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2029_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2029_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2030_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2030_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2030_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2030_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2031_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2031_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2031_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2031_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2032_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2032_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2032_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2032_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2033_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2033_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2033_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2033_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2034_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2034_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2034_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2034_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2035_Q1`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2035_Q2`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2035_Q3`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;

ALTER TABLE `kc_clearing_info_2035_Q4`
ADD COLUMN  `masked_card_no` varchar(24)  DEFAULT NULL COMMENT '脱敏卡号' AFTER `card_no`,
ADD COLUMN  `settlement_flag` varchar(4)  DEFAULT NULL COMMENT '清算标志',
ADD COLUMN  `delete_flag` int(4)  DEFAULT '0' COMMENT '删除标记;0:未删除;1:已删除',
DROP COLUMN `card_last_four`;


ALTER TABLE `kc_exception_info`
CHANGE COLUMN `card_last_four` `masked_card_no` varchar(24) NULL DEFAULT NULL COMMENT '脱敏卡号' AFTER `kcard_id`;


ALTER TABLE `kc_clearing_error`
CHANGE COLUMN `card_last_four` `masked_card_no` varchar(24) NULL DEFAULT NULL COMMENT '脱敏卡号' AFTER `kcard_id`;

-- 清分表新增原清分id字段
alter table kl_clearing_trans_202505 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202506 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202507 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202508 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202509 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202510 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202511 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202512 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202601 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202602 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202603 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202604 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202605 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202606 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202607 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202608 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202609 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202610 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202611 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202612 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202701 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202702 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202703 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202704 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202705 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202706 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202707 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202708 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202709 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202710 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202711 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202712 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202801 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202802 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202803 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202804 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202805 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202806 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202807 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202808 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202809 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202810 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202811 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202812 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202901 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202902 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202903 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202904 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202905 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202906 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202907 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202908 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202909 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202910 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202911 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_202912 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203001 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203002 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203003 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203004 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203005 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203006 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203007 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203008 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203009 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203010 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203011 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;
alter table kl_clearing_trans_203012 add column original_clearing_id varchar(64) default null comment '原清分id' after card_schema_product_id;

-- 历史记录值补充
update kl_clearing_trans_202505 t1
    join kl_clearing_trans_202505 t2 on t2.gateway_clearing_id = t1.original_gateway_clearing_id
    set t1.original_clearing_id = t2.clearing_id
where t1.original_gateway_clearing_id is not null
  and t1.original_clearing_id is null;

update kl_clearing_trans_202506 t1
    join (select clearing_id, gateway_clearing_id
    from kl_clearing_trans_202505
    union all
    select clearing_id, gateway_clearing_id
    from kl_clearing_trans_202506) t2 on t2.gateway_clearing_id = t1.original_gateway_clearing_id
    set t1.original_clearing_id = t2.clearing_id
where t1.original_gateway_clearing_id is not null
  and t1.original_clearing_id is null;

update kl_clearing_trans_202507 t1
    join (select clearing_id, gateway_clearing_id
    from kl_clearing_trans_202505
    union all
    select clearing_id, gateway_clearing_id
    from kl_clearing_trans_202506
    union all
    select clearing_id, gateway_clearing_id
    from kl_clearing_trans_202507) t2 on t2.gateway_clearing_id = t1.original_gateway_clearing_id
    set t1.original_clearing_id = t2.clearing_id
where t1.original_gateway_clearing_id is not null
  and t1.original_clearing_id is null;