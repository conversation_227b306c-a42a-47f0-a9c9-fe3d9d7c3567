package com.kun.linkage.auth.mq;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.auth.facade.vo.mq.OrganizationTransAccountingReversalVO;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(topic = MqTopicConstant.ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC,
    consumerGroup = MqConsumerGroupConstant.KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP, messageModel = MessageModel.CLUSTERING)
public class OrganizationTransAccountingReversalListener
    implements RocketMQListener<OrganizationTransAccountingReversalVO> {

    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;
    @Resource
    private Tracer tracer;

    @NewSpan
    @Override
    public void onMessage(OrganizationTransAccountingReversalVO message) {
        // 设置日志追踪上下文
        if (tracer.currentSpan() != null && message.getLogContext() != null) {
            tracer.currentSpan().tag("traceId", message.getLogContext().getTraceId());
        }
        log.info("[商户交易账冲账]接收到消息: {}", JSON.toJSONString(message));
    }
}
