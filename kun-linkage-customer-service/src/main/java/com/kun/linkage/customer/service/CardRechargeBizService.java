package com.kun.linkage.customer.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.req.AccountReversalReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageParam;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.*;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDebitSubRefundStatusEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.*;
import com.kun.linkage.common.external.facade.api.kcard.res.*;
import com.kun.linkage.customer.ext.mapper.CardRechargeDetailExtMapper;
import com.kun.linkage.customer.facade.api.bean.req.CardRechargeReq;
import com.kun.linkage.customer.facade.api.bean.req.CardRechargeStatusQueryReq;
import com.kun.linkage.customer.facade.api.bean.req.PageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.CardRechargeRes;
import com.kun.linkage.customer.facade.api.bean.res.CardRechargeStatusQueryRes;
import com.kun.linkage.customer.facade.api.bean.res.PageQueryCardRechargeDetailRes;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.facade.vo.mq.CardRechargeBookkeepReversalEventVO;
import com.kun.linkage.customer.service.organization.OrganizationFeeConfigBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;

/**
 * <p>
 * 卡充值业务服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class CardRechargeBizService {
    private static final Logger log = LoggerFactory.getLogger(CardRechargeBizService.class);
    @Resource
    private CardRechargeDetailMapper cardRechargeDetailMapper;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;
    @Resource
    private OrganizationCustomerAccountInfoMapper organizationCustomerAccountInfoMapper;
    @Resource
    private CardRechargeDetailExtMapper cardRechargeDetailExtMapper;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;
    @Resource
    private AccountTransactionFacade accountTransactionFacade;
    @Resource
    private OrganizationFeeConfigBizService organizationFeeConfigBizService;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private CardRechargeBizService cardRechargeBizService;
    @Resource
    private RocketMqService rocketMqService;


    /**
     * 卡充值
     *
     * @param cardRechargeReq
     * @return
     */
    public Result<CardRechargeRes> cardRecharge(CardRechargeReq cardRechargeReq) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        // 校验是否重复请求
        Long num = cardRechargeDetailMapper.selectCount(Wrappers.<CardRechargeDetail>lambdaQuery()
                .eq(CardRechargeDetail::getOrganizationNo, cardRechargeReq.getOrganizationNo())
                .eq(CardRechargeDetail::getRequestNo, cardRechargeReq.getRequestNo())
                .between(CardRechargeDetail::getRechargeDatetime, now.minusDays(360), now));
        if (num != null && num > 0) {
            log.error("[卡片管理-卡充值]重复请求,机构号:{},请求流水号:{},时间范围:{}-{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getRequestNo(), now.minusDays(360), now);
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 充值币种暂时只支持美金和港币
        if (!FiatCurrencyEnum.contains(cardRechargeReq.getRechargeCurrencyCode())) {
            log.error("[卡片管理-卡充值]不支持的充值币种,机构号:{},请求流水号:{},充值币种:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getRequestNo(),
                    cardRechargeReq.getRechargeCurrencyCode());
            return Result.fail(CustomerTipConstant.CURRENCY_NOT_SUPPORT);
        }
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, cardRechargeReq.getOrganizationNo()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-卡充值]机构信息不存在,机构号:{}", cardRechargeReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        if (!StringUtils.equals(ValidStatusEnum.VALID.getValue(), organizationBasicInfo.getStatus())) {
            log.error("[卡片管理-卡充值]机构状态异常,机构号:{}", cardRechargeReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_STATUS_IS_NOT_NORMAL);
        }
        if (StringUtils.isBlank(organizationBasicInfo.getPoolCurrencyCode())) {
            log.error("[卡片管理-卡充值]机构资金池币种未配置,机构号:{}", cardRechargeReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_POOL_CURRENCY_CODE_NOT_CONFIG);
        }
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cardRechargeReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cardRechargeReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cardRechargeReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-卡充值]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getCustomerId(), cardRechargeReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        if (!StringUtils.equals(cardRechargeReq.getRechargeCurrencyCode(), organizationCustomerCardInfo.getCurrencyCode())) {
            log.error("[卡片管理-卡充值]充值币种不是卡币种,机构号:{},客户号:{},卡id:{},充值币种:{},卡币种:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getCustomerId(), cardRechargeReq.getCardId(),
                    cardRechargeReq.getRechargeCurrencyCode(), organizationCustomerCardInfo.getCurrencyCode());
            return Result.fail(CustomerTipConstant.RECHARGE_CURRENCY_MUST_BE_THE_CARD_CURRENCY);
        }
        if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.CANCEL.getStatus())) {
            log.warn("[卡片管理-卡充值]卡已注销,机构号:{},客户号:{},卡id:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getCustomerId(), cardRechargeReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_HAS_BEEN_CANCELLED);
        }
        if (!StringUtils.equals(organizationCustomerCardInfo.getCardActiveStatus(), CardActiveStatusEnum.ACTIVATED.getStatus())) {
            log.warn("[卡片管理-卡充值]卡未激活,机构号:{},客户号:{},卡id:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getCustomerId(), cardRechargeReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_IS_NOT_ACTIVATED);
        }
        // 获取用户账户信息
        OrganizationCustomerAccountInfo organizationCustomerAccountInfo = organizationCustomerAccountInfoMapper.selectOne(
                Wrappers.<OrganizationCustomerAccountInfo>lambdaQuery()
                        .eq(OrganizationCustomerAccountInfo::getOrganizationNo, cardRechargeReq.getOrganizationNo())
                        .eq(OrganizationCustomerAccountInfo::getCustomerId, cardRechargeReq.getCustomerId())
                        .eq(OrganizationCustomerAccountInfo::getCurrencyCode, cardRechargeReq.getRechargeCurrencyCode())
                        .eq(OrganizationCustomerAccountInfo::getAccountType, BusinessAccountTypeEnum.BASIC.getValue())
                        .eq(OrganizationCustomerAccountInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationCustomerAccountInfo == null || StringUtils.isBlank(organizationCustomerAccountInfo.getAccountNo())) {
            log.error("[卡片管理-卡充值]不存在有效的用户法币账户信息,机构号:{},客户号:{},币种:{}",
                    cardRechargeReq.getOrganizationNo(), cardRechargeReq.getCustomerId(), cardRechargeReq.getRechargeCurrencyCode());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }
        // 进行充值
        CardRechargeDetail cardRechargeDetail = null;
        try {
            // 初始化充值记录
            cardRechargeDetail = this.initCardRechargeDetail(cardRechargeReq, organizationBasicInfo.getPoolCurrencyCode());
            if (DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                // 扣除币种是数币
                log.info("[卡片管理-卡充值]扣除币种是数币,开始进行处理");
                cardRechargeBizService.processingDigitalCurrency(cardRechargeDetail, organizationBasicInfo,
                        organizationCustomerAccountInfo.getAccountNo(), organizationCustomerCardInfo.getCardProductCode());
            } else {
                // 扣除币种是法币
                log.info("[卡片管理-卡充值]扣除币种是法币,开始进行处理");
                cardRechargeBizService.processingFiatCurrency(cardRechargeDetail, organizationBasicInfo,
                        organizationCustomerAccountInfo.getAccountNo(), organizationCustomerCardInfo.getCardProductCode());
            }
            // 充值成功
            cardRechargeDetail.setRechargeStatus(OperationStatusEnum.SUCCESS.getStatus());
            CardRechargeRes cardRechargeRes = new CardRechargeRes();
            cardRechargeRes.setCardId(cardRechargeDetail.getCardId());
            cardRechargeRes.setRechargeAmount(cardRechargeDetail.getRechargeAmount());
            cardRechargeRes.setRechargeCurrencyCode(cardRechargeDetail.getRechargeCurrencyCode());
            cardRechargeRes.setDeductAmount(cardRechargeDetail.getDeductTotalAmount());
            cardRechargeRes.setDeductCurrencyCode(cardRechargeDetail.getDeductCurrencyCode());
            return Result.success(cardRechargeRes);
        } catch (BusinessException be) {
            log.error("[卡片管理-卡充值]卡充值处理失败,异常信息:{}", be.getMessage());
            if (cardRechargeDetail != null) {
                cardRechargeDetail.setRechargeStatus(OperationStatusEnum.FAIL.getStatus());
                cardRechargeDetail.setDeductRechargeFeeDetailId(null);
                cardRechargeDetail.setDeductAcceptanceFeeDetailId(null);
            }
            return Result.fail(CommonTipConstant.FAIL);
        } catch (Exception e) {
            log.error("[卡片管理-卡充值]卡充值处理异常,异常信息:", e);
            if (cardRechargeDetail != null) {
                cardRechargeDetail.setRechargeStatus(OperationStatusEnum.FAIL.getStatus());
                cardRechargeDetail.setDeductRechargeFeeDetailId(null);
                cardRechargeDetail.setDeductAcceptanceFeeDetailId(null);
            }
            return Result.fail(CommonTipConstant.FAIL);
        } finally {
            // 更新充值结果
            if (cardRechargeDetail != null) {
                cardRechargeDetailMapper.update(cardRechargeDetail, Wrappers.<CardRechargeDetail>lambdaQuery()
                        .eq(CardRechargeDetail::getId, cardRechargeDetail.getId())
                        .eq(CardRechargeDetail::getRechargeDatetime, cardRechargeDetail.getRechargeDatetime()));
            }
            log.info("[卡片管理-卡充值]处理完成");
        }
    }

    /**
     * 处理扣除币种为数币的请求
     *
     * @param cardRechargeDetail
     * @param organizationBasicInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void processingDigitalCurrency(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo, String accountNo, String cardProductCode) {
        cardRechargeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
        // 汇率换算
        this.processingDigitalCurrencyExchangeRate(cardRechargeDetail, organizationBasicInfo);
        // 计算手续费
        this.calculateFeeAmount(cardRechargeDetail, cardProductCode, true);
        // 扣除总金额
        cardRechargeDetail.setDeductTotalAmount(cardRechargeDetail.getDeductPrincipalAmount()
                .add(cardRechargeDetail.getDeductRechargeFeeAmount()).add(cardRechargeDetail.getDeductAcceptanceFeeAmount()));
        // 调用账户动账
        try {
            this.processingDigitalCurrencyAccountTransaction(cardRechargeDetail, organizationBasicInfo, accountNo);
        } catch (Exception e) {
            log.error("[卡片管理-卡充值]扣账币种为数币的账户动账失败,发送冲账事件到mq", e);
            this.sendCardRechargeBookkeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
            throw e;
        }
    }

    /**
     * 处理扣除币种为法币的请求
     *
     * @param cardRechargeDetail
     * @param organizationBasicInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void processingFiatCurrency(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo, String accountNo, String cardProductCode) {
        cardRechargeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
        // 汇率换算
        this.processingFiatCurrencyExchangeRate(cardRechargeDetail, organizationBasicInfo);
        // 计算手续费
        this.calculateFeeAmount(cardRechargeDetail, cardProductCode, false);
        // 扣除总金额
        cardRechargeDetail.setDeductTotalAmount(cardRechargeDetail.getDeductPrincipalAmount()
                .add(cardRechargeDetail.getDeductRechargeFeeAmount()).add(cardRechargeDetail.getDeductAcceptanceFeeAmount()));
        // 调用账户动账
        try {
            this.processingFiatCurrencyAccountTransaction(cardRechargeDetail, organizationBasicInfo, accountNo);
        } catch (Exception e) {
            log.error("[卡片管理-卡充值]扣账币种为法币的账户动账失败,发送冲账事件到mq", e);
            this.sendCardRechargeBookkeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
            throw e;
        }
    }

    /**
     * 进行法币转数币汇率换算
     *
     * @param cardRechargeDetail
     */
    private void processingDigitalCurrencyExchangeRate(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo) {
        log.info("[卡片管理-卡充值]开始进行法币转数币汇率换算");
        // 美金兑USDC需要查最新汇率,兑USDT不需要
        // 港币都需要查询最新汇率
        BigDecimal fxRate = BigDecimal.ONE;
        if (!(StringUtils.equals(cardRechargeDetail.getRechargeCurrencyCode(), FiatCurrencyEnum.USD.getCurrencyCode())
                && StringUtils.equals(cardRechargeDetail.getDeductCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后
            KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(cardRechargeDetail, organizationBasicInfo);
            log.info("[卡片管理-卡充值]调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
            Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
            log.info("[卡片管理-卡充值]调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                /*// 此处应使用kun接口返回汇率的导数
                 fxRate = BigDecimal.ONE.divide(kunAskPriceRspResult.getData().getPrice(), 5, RoundingMode.DOWN);*/
                // 现在他们的接口又改成返回的是正确的汇率了,先暂时改回,后面等他确认后再改
                fxRate = kunAskPriceRspResult.getData().getPrice();
            } else {
                log.error("[卡片管理-卡充值]调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        cardRechargeDetail.setFxRate(fxRate);
        cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate)
                .setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));
        log.info("[卡片管理-卡充值]法币转数币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                cardRechargeDetail.getRechargeCurrencyCode(), cardRechargeDetail.getDeductCurrencyCode(), fxRate);
    }

    /**
     * 进行法币转法币汇率换算
     *
     * @param cardRechargeDetail
     */
    private void processingFiatCurrencyExchangeRate(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo) {
        log.info("[卡片管理-卡充值]开始进行法币转法币汇率换算");
        // 充值币种和扣减币种不一致才需要换汇
        BigDecimal fxRate = BigDecimal.ONE;
        if (!StringUtils.equals(cardRechargeDetail.getDeductCurrencyCode(), cardRechargeDetail.getRechargeCurrencyCode())) {
            PayXAskPriceReq payXAskPriceReq = this.assPayXAskPriceReq(cardRechargeDetail, organizationBasicInfo);
            log.info("[卡片管理-卡充值]调用PayX汇率查询接口开始,请求参数:{}", JSON.toJSONString(payXAskPriceReq));
            Result<PayXAskPriceRsp> payXAskPriceRspResult = kCardPayXAccountFacade.payXExchangeRate(payXAskPriceReq);
            log.info("[卡片管理-卡充值]调用PayX汇率查询接口结束,响应参数:{}", JSON.toJSONString(payXAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (payXAskPriceRspResult != null && StringUtils.equals(payXAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && payXAskPriceRspResult.getData() != null && payXAskPriceRspResult.getData().getExchangeRate() != null) {
                fxRate = payXAskPriceRspResult.getData().getExchangeRate();
            } else {
                log.error("[卡片管理-卡充值]调用PayX汇率查询接口失败,响应信息:{}", fxRate);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        cardRechargeDetail.setFxRate(fxRate);
        cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate)
                .setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));
        log.info("[卡片管理-卡充值]法币转法币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                cardRechargeDetail.getRechargeCurrencyCode(), cardRechargeDetail.getDeductCurrencyCode(), fxRate);
    }

    /**
     * 计算手续费
     *
     * @param cardRechargeDetail
     */
    private void calculateFeeAmount(CardRechargeDetail cardRechargeDetail, String cardProductCode, boolean calculateRechargeAcceptanceFeeFlag) {
        log.info("[卡片管理-卡充值]开始计算手续费");
        cardRechargeDetail.setDeductRechargeFeeAmount(BigDecimal.ZERO);
        cardRechargeDetail.setDeductAcceptanceFeeAmount(BigDecimal.ZERO);
        if (!calculateRechargeAcceptanceFeeFlag) {
            cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
        }
        // 获取费率配置
        OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigBizService.getValidOrganizationFeeConfigByWhere(cardRechargeDetail.getOrganizationNo(), cardProductCode);
        if (organizationFeeConfig == null) {
            log.warn("[卡片管理-卡充值]未找到机构费率配置信息,手续费按0处理,机构号:{},卡产品编号:{}", cardRechargeDetail.getOrganizationNo(), cardProductCode);
        } else {
            // 充值手续费相关
            log.info("[卡片管理-卡充值]开始计算充值手续费");
            this.calculateAndSaveFeeDetail(cardRechargeDetail, organizationFeeConfig.getTemplateNo(), cardProductCode,
                    OrganizationFeeTypeEnum.RECHARGE_FEE, KunAndPayXRemarkEnum.RECHARGE_CARD_FEE.getRemark());
            log.info("[卡片管理-卡充值]充值手续费计算完成,最终充值手续费为:{}, 币种:{}", cardRechargeDetail.getDeductRechargeFeeAmount(), cardRechargeDetail.getDeductCurrencyCode());

            // 需要计算充值承兑费
            if (calculateRechargeAcceptanceFeeFlag) {
                log.info("[卡片管理-卡充值]开始计算充值承兑费");
                this.calculateAndSaveFeeDetail(cardRechargeDetail, organizationFeeConfig.getTemplateNo(), cardProductCode,
                        OrganizationFeeTypeEnum.ACCEPTANCE_FEE, KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
                log.info("[卡片管理-卡充值]充值承兑费计算完成,最终充值承兑费为:{}, 币种:{}", cardRechargeDetail.getDeductAcceptanceFeeAmount(), cardRechargeDetail.getDeductCurrencyCode());
            } else {
                log.warn("[卡片管理-卡充值]不需要计算充值承兑费");
            }
        }
    }

    /**
     * 计算并保存费用明细
     *
     * @param cardRechargeDetail
     */
    private void calculateAndSaveFeeDetail(CardRechargeDetail cardRechargeDetail, String templateNo, String cardProductCode, OrganizationFeeTypeEnum feeTypeEnum, String remark) {
        OrganizationFeeTemplateDetail organizationFeeTemplateDetail = organizationFeeTemplateDetailMapper.selectOne(
                Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetail::getTemplateNo, templateNo)
                        .eq(OrganizationFeeTemplateDetail::getFeeType, feeTypeEnum.getValue())
                        .eq(OrganizationFeeTemplateDetail::getCurrencyCode, cardRechargeDetail.getRechargeCurrencyCode())
                        .in(OrganizationFeeTemplateDetail::getBillingDimension,
                                Arrays.asList(OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue(), OrganizationFeeBillingDimensionEnum.TIERED_SINGLE_AMOUNT.getValue()))
                        // 左开右闭
                        .lt(OrganizationFeeTemplateDetail::getMinAmount, cardRechargeDetail.getRechargeAmount())
                        .ge(OrganizationFeeTemplateDetail::getMaxAmount, cardRechargeDetail.getRechargeAmount()));
        if (organizationFeeTemplateDetail == null) {
            log.warn("[卡片管理-卡充值]费率类型:{},未找到机构费率配置明细,手续费按0处理,机构号:{},卡产品编号:{},币种:{},模版号:{}", feeTypeEnum.getValue(),
                    cardRechargeDetail.getOrganizationNo(), cardProductCode, cardRechargeDetail.getRechargeCurrencyCode(), templateNo);
        } else {
            BigDecimal proportionRate = organizationFeeTemplateDetail.getProportionRate() != null ? organizationFeeTemplateDetail.getProportionRate() : BigDecimal.ZERO;
            BigDecimal fixedAmount = organizationFeeTemplateDetail.getFixedAmount() != null ? organizationFeeTemplateDetail.getFixedAmount() : BigDecimal.ZERO;
            BigDecimal proportionFeeAmount = cardRechargeDetail.getRechargeAmount().multiply(proportionRate);
            if (organizationFeeTemplateDetail.getProportionMinAmount() != null
                    && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMinAmount()) < 0) {
                proportionFeeAmount = organizationFeeTemplateDetail.getProportionMinAmount();
            } else if (organizationFeeTemplateDetail.getProportionMaxAmount() != null
                    && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMaxAmount()) > 0) {
                proportionFeeAmount = organizationFeeTemplateDetail.getProportionMaxAmount();
            }
            BigDecimal feeAmount = proportionFeeAmount.add(fixedAmount);
            BigDecimal deductFeeAmount = feeAmount.multiply(cardRechargeDetail.getFxRate()).setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP);
            if (deductFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 保存费用明细记录
                LocalDateTime now = LocalDateTime.now().withNano(0);
                OrganizationFeeDetail organizationFeeDetail = new OrganizationFeeDetail();
                organizationFeeDetail.setOrganizationNo(cardRechargeDetail.getOrganizationNo());
                organizationFeeDetail.setCardProductCode(cardProductCode);
                organizationFeeDetail.setRelatedTransactionId(cardRechargeDetail.getRequestNo());
                organizationFeeDetail.setCalculateDatetime(now);
                organizationFeeDetail.setTransactionDatetime(now);
                organizationFeeDetail.setFeeType(organizationFeeTemplateDetail.getFeeType());
                organizationFeeDetail.setFeeCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
                organizationFeeDetail.setTransactionAmount(cardRechargeDetail.getRechargeAmount());
                organizationFeeDetail.setTransactionCurrencyCode(cardRechargeDetail.getRechargeCurrencyCode());
                organizationFeeDetail.setTransactionCurrencyPrecision(cardRechargeDetail.getRechargeCurrencyPrecision());
                organizationFeeDetail.setFeeAmount(feeAmount);
                organizationFeeDetail.setSnapshotBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
                organizationFeeDetail.setSnapshotMinAmount(organizationFeeTemplateDetail.getMinAmount());
                organizationFeeDetail.setSnapshotMaxAmount(organizationFeeTemplateDetail.getMaxAmount());
                organizationFeeDetail.setSnapshotProportionRate(proportionRate);
                organizationFeeDetail.setSnapshotProportionMinAmount(organizationFeeTemplateDetail.getProportionMinAmount());
                organizationFeeDetail.setSnapshotProportionMaxAmount(organizationFeeTemplateDetail.getProportionMaxAmount());
                organizationFeeDetail.setSnapshotFixedAmount(fixedAmount);
                String requestNo = null;
                organizationFeeDetail.setDeductProcessor(cardRechargeDetail.getDeductProcessor());
                organizationFeeDetail.setDeductCurrencyCode(cardRechargeDetail.getDeductCurrencyCode());
                organizationFeeDetail.setDeductCurrencyPrecision(cardRechargeDetail.getDeductCurrencyPrecision());
                organizationFeeDetail.setDeductFeeAmount(deductFeeAmount);
                if (StringUtils.equals(OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue(), organizationFeeTemplateDetail.getCollectionMethod())) {
                    // 实时收取的直接设置为已收
                    organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.COLLECTED.getValue());
                    // 如果为实时收取直接在此处生成调用kun的请求流水号
                    requestNo = String.valueOf(IdWorker.getId());
                    organizationFeeDetail.setDeductRequestNo(requestNo);
                    organizationFeeDetail.setCallCount(1);
                } else {
                    organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.NOT_COLLECTED.getValue());
                    organizationFeeDetail.setCallCount(0);
                }
                organizationFeeDetail.setRemark(remark);
                organizationFeeDetail.setCreateTime(now);
                organizationFeeDetail.setLastModifyTime(now);
                organizationFeeDetailMapper.insert(organizationFeeDetail);
                log.info("[卡片管理-卡充值]费率类型:{}, 生成费用明细记录成功,记录id:{}", organizationFeeTemplateDetail.getFeeType(), organizationFeeDetail.getId());
                if (StringUtils.equals(feeTypeEnum.getValue(), OrganizationFeeTypeEnum.RECHARGE_FEE.getValue())) {
                    cardRechargeDetail.setDeductRechargeFeeDetailId(organizationFeeDetail.getId());
                    cardRechargeDetail.setDeductRechargeFeeAmount(deductFeeAmount);
                    if (StringUtils.equals(OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue(), organizationFeeTemplateDetail.getCollectionMethod())) {
                        // 实时扣账需要将请求流水号先放入实体类中,后面直接使用
                        cardRechargeDetail.setDeductRechargeFeeBookkeepRequestNo(requestNo);
                    } else {
                        // 充值手续费会有月结和实时两种情况,月结的无需实时扣账,直接将记账状态设置为无需记账,设置之后后面扣账直接跳过
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
                    }
                } else {
                    cardRechargeDetail.setDeductAcceptanceFeeDetailId(organizationFeeDetail.getId());
                    cardRechargeDetail.setDeductAcceptanceFeeAmount(deductFeeAmount);
                    // 承兑费只有实时收取
                    cardRechargeDetail.setDeductAcceptanceFeeBookkeepRequestNo(requestNo);
                }
            } else {
                log.info("[卡片管理-卡充值]费率类型:{}, 费用为0,不保存费用记录明细", organizationFeeTemplateDetail.getFeeType());
            }
        }
    }

    /**
     * 处理扣账币种为数币的账户动账
     *
     * @param cardRechargeDetail
     */
    private void processingDigitalCurrencyAccountTransaction(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo, String customerAccountNo) {
        log.info("[卡片管理-卡充值]开始处理扣账币种为数币的账户动账");
        // 扣除机构本金金额
        if (cardRechargeDetail.getDeductPrincipalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 调用kun进行扣账
            KunDebitSubReq kunDebitSubReq = this.assBaseKunDebitSubReq(organizationBasicInfo);
            String requestNo = String.valueOf(IdWorker.getId());
            kunDebitSubReq.setRequestNo(requestNo);
            cardRechargeDetail.setDeductPrincipalBookkeepRequestNo(requestNo);
            kunDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
            kunDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
            kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.RECHARGE.getRemark());
            log.info("[卡片管理-卡充值]调用KUN账户扣除本金开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
            Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
            log.info("[卡片管理-卡充值]调用KUN账户扣除本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[卡片管理-卡充值]调用KUN账户扣除本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账
                    log.error("[卡片管理-卡充值]调用KUN账户扣除本金明确失败");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[卡片管理-卡充值]调用KUN账户扣除本金状态未知");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                log.error("[卡片管理-卡充值]调用KUN账户扣除本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } else {
            log.info("[卡片管理-卡充值]本金金额不大于0,本金无需动账");
            cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
        }

        // 充值手续费如果是月结的前面会设置无需记账,无需调用kun进行扣账
        if (CardRechargeBookkeepStatusEnum.NO_NEED.getValue().equals(cardRechargeDetail.getDeductRechargeFeeBookkeepStatus())) {
            log.info("[卡片管理-卡充值]充值手续费配置为月结,无需实时扣kun的账");
        } else {
            // 扣除机构充值手续费金额
            if (cardRechargeDetail.getDeductRechargeFeeAmount().compareTo(BigDecimal.ZERO) > 0
                    && StringUtils.isNotBlank(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo())) {
                // 调用kun进行扣账
                KunDebitSubReq kunDebitSubReq = this.assBaseKunDebitSubReq(organizationBasicInfo);
                kunDebitSubReq.setRequestNo(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo());
                kunDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
                kunDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
                kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.RECHARGE_CARD_FEE.getRemark());
                log.info("[卡片管理-卡充值]调用KUN账户扣除充值手续费开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
                Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
                log.info("[卡片管理-卡充值]调用KUN账户扣除充值手续费结束,响应参数:{}", JSON.toJSONString(result));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                    if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                        // 明确成功
                        log.info("[卡片管理-卡充值]调用KUN账户扣除充值手续费成功");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                    } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                        // 明确失败不需要冲账,但要处理对已动账的金额进行冲账
                        log.info("[卡片管理-卡充值]调用KUN账户扣除充值手续费明确失败");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    } else {
                        log.info("[卡片管理-卡充值]调用KUN账户扣除充值手续费状态未知");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    }
                } else {
                    cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    log.error("[卡片管理-卡充值]调用KUN账户扣除充值手续费状态未知");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.info("[卡片管理-卡充值]充值手续费不大于0,充值手续费无需动账");
                cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
            }
        }

        // 扣除机构充值承兑费金额
        if (cardRechargeDetail.getDeductAcceptanceFeeAmount().compareTo(BigDecimal.ZERO) > 0
                && StringUtils.isNotBlank(cardRechargeDetail.getDeductAcceptanceFeeBookkeepRequestNo())) {
            // 调用kun进行扣账
            KunDebitSubReq kunDebitSubReq = this.assBaseKunDebitSubReq(organizationBasicInfo);
            kunDebitSubReq.setRequestNo(cardRechargeDetail.getDeductAcceptanceFeeBookkeepRequestNo());
            kunDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
            kunDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
            kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
            log.info("[卡片管理-卡充值]调用KUN账户扣除充值承兑费开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
            Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
            log.info("[卡片管理-卡充值]调用KUN账户扣除充值承兑费结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[卡片管理-卡充值]调用KUN账户扣除充值承兑费成功");
                    cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账,但要处理对已动账的金额进行冲账
                    log.info("[卡片管理-卡充值]调用KUN账户扣除充值承兑费明确失败");
                    cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.info("[卡片管理-卡充值]调用KUN账户扣除充值承兑费状态未知");
                    cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                log.error("[卡片管理-卡充值]调用KUN账户扣除充值承兑费状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } else {
            log.info("[卡片管理-卡充值]充值承兑费不大于0,充值承兑费无需动账");
            cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
        }

        // 调用账户服务给客户进行上账
        AccountChangeBalanceReq accountChangeBalanceReq = this.assAccountChangeBalanceReq(cardRechargeDetail, customerAccountNo);
        log.info("[卡片管理-卡充值]调用账户服务给客户上账开始,请求参数:{}", JSON.toJSONString(accountChangeBalanceReq));
        Result<AccountChangeBalanceRes> accountChangeBalanceResResult = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
        log.info("[卡片管理-卡充值]调用账户服务给客户上账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
        if (Result.isSuccess(accountChangeBalanceResResult)) {
            // 有明确成功状态
            log.info("[卡片管理-卡充值]调用账户服务给客户上账成功");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
        } else if (accountChangeBalanceResResult != null && CommonTipConstant.REQUEST_TIMEOUT.equals(accountChangeBalanceResResult.getCode())) {
            log.error("[卡片管理-卡充值]调用账户服务给客户上账超时");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        } else {
            log.error("[卡片管理-卡充值]调用账户服务给客户上账失败");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
        log.info("[卡片管理-卡充值]扣账币种为数币的账户动账完成");
    }

    /**
     * 处理扣账币种为数币的账户动账
     *
     * @param cardRechargeDetail
     */
    private void processingFiatCurrencyAccountTransaction(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo, String customerAccountNo) {
        log.info("[卡片管理-卡充值]开始处理扣账币种为法币的账户动账");
        // 扣除机构本金金额
        if (cardRechargeDetail.getDeductPrincipalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 调用PayX进行扣账
            PayXDebitSubReq payXDebitSubReq = this.assBasePayXDebitSubReq(organizationBasicInfo);
            String requestNo = String.valueOf(IdWorker.getId());
            payXDebitSubReq.setRequestNo(requestNo);
            cardRechargeDetail.setDeductPrincipalBookkeepRequestNo(requestNo);
            payXDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
            payXDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
            payXDebitSubReq.setRemark(KunAndPayXRemarkEnum.RECHARGE.getRemark());
            log.info("[卡片管理-卡充值]调用PayX账户扣除本金开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
            Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
            log.info("[卡片管理-卡充值]调用PayX账户扣除本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[卡片管理-卡充值]调用PayX账户扣除本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账
                    log.error("[卡片管理-卡充值]调用PayX账户扣除本金明确失败");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[卡片管理-卡充值]调用PayX账户扣除本金状态未知");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                log.error("[卡片管理-卡充值]调用PayX账户扣除本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } else {
            log.info("[卡片管理-卡充值]本金金额不大于0,本金无需动账");
            cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
        }

        // 充值手续费如果是月结的前面会设置无需记账,无需调用PayX进行扣账
        if (CardRechargeBookkeepStatusEnum.NO_NEED.getValue().equals(cardRechargeDetail.getDeductRechargeFeeBookkeepStatus())) {
            log.info("[卡片管理-卡充值]充值手续费配置为月结,无需实时扣PayX的账");
        } else {
            // 扣除机构充值手续费金额
            if (cardRechargeDetail.getDeductRechargeFeeAmount().compareTo(BigDecimal.ZERO) > 0
                    && StringUtils.isNotBlank(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo())) {
                // 调用PayX进行扣账
                PayXDebitSubReq payXDebitSubReq = this.assBasePayXDebitSubReq(organizationBasicInfo);
                payXDebitSubReq.setRequestNo(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo());
                payXDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
                payXDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
                payXDebitSubReq.setRemark(KunAndPayXRemarkEnum.RECHARGE_CARD_FEE.getRemark());
                log.info("[卡片管理-卡充值]调用PayX账户扣除充值手续费开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
                Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
                log.info("[卡片管理-卡充值]调用PayX账户扣除充值手续费结束,响应参数:{}", JSON.toJSONString(result));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                    if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                        // 明确成功
                        log.info("[卡片管理-卡充值]调用PayX账户扣除充值手续费成功");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                    } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                        // 明确失败不需要冲账,但要处理对已动账的金额进行冲账
                        log.info("[卡片管理-卡充值]调用PayX账户扣除充值手续费明确失败");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    } else {
                        log.info("[卡片管理-卡充值]调用PayX账户扣除充值手续费状态未知");
                        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    }
                } else {
                    cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    log.error("[卡片管理-卡充值]调用PayX账户扣除充值手续费状态未知");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.info("[卡片管理-卡充值]充值手续费不大于0,充值手续费无需动账");
                cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
            }
        }

        // 调用账户服务给客户进行上账
        AccountChangeBalanceReq accountChangeBalanceReq = this.assAccountChangeBalanceReq(cardRechargeDetail, customerAccountNo);
        log.info("[卡片管理-卡充值]调用账户服务给客户上账开始,请求参数:{}", JSON.toJSONString(accountChangeBalanceReq));
        Result<AccountChangeBalanceRes> accountChangeBalanceResResult = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
        log.info("[卡片管理-卡充值]调用账户服务给客户上账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
        if (Result.isSuccess(accountChangeBalanceResResult)) {
            // 有明确成功状态
            log.info("[卡片管理-卡充值]调用账户服务给客户上账成功");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
        } else if (accountChangeBalanceResResult != null && CommonTipConstant.REQUEST_TIMEOUT.equals(accountChangeBalanceResResult.getCode())) {
            log.error("[卡片管理-卡充值]调用账户服务给客户上账超时");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        } else {
            log.error("[卡片管理-卡充值]调用账户服务给客户上账失败");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
        log.info("[卡片管理-卡充值]扣账币种为法币的账户动账完成");
    }

    /**
     * 处理扣账币种为数币的账户冲账
     *
     * @param cardRechargeDetail
     */
    public void processingDigitalCurrencyBookkeepReversal(CardRechargeDetail cardRechargeDetail, String mpcToken, String mpcGroupCode) {
        log.info("[卡充值记账冲正通知事件]开始处理扣账币种为数币的账户冲账");
        // 已记账或者是状态未知的需要进行冲账
        if (cardRechargeDetail.getDeductPrincipalBookkeepStatus() != null
                && (cardRechargeDetail.getDeductPrincipalBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getDeductPrincipalBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            // 调用kun进行冲账
            KunDebitSubRefundReq kunDebitSubRefundReq = this.assBaseKunDebitSubRefundReq(mpcToken, mpcGroupCode, cardRechargeDetail.getOrganizationNo());
            kunDebitSubRefundReq.setSourceRequestNo(cardRechargeDetail.getDeductPrincipalBookkeepRequestNo());
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账本金开始,请求参数:{}", JSON.toJSONString(kunDebitSubRefundReq));
            Result<KunDebitSubRefundRsp> result = kCardKunAccountFacade.kunDebitSubRefund(kunDebitSubRefundReq);
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            // TODO 数据不存在的情况
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                    // 撤销完成和失败禁止撤销都算冲账成功
                    log.info("[卡充值记账冲正通知事件]调用KUN账户冲账本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                } else {
                    log.error("[卡充值记账冲正通知事件]调用KUN账户冲账本金失败");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[卡充值记账冲正通知事件]调用KUN账户冲账本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }

        if (cardRechargeDetail.getDeductRechargeFeeBookkeepStatus() != null
                && (cardRechargeDetail.getDeductRechargeFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getDeductRechargeFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            // 调用kun进行冲账
            KunDebitSubRefundReq kunDebitSubRefundReq = this.assBaseKunDebitSubRefundReq(mpcToken, mpcGroupCode, cardRechargeDetail.getOrganizationNo());
            kunDebitSubRefundReq.setSourceRequestNo(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo());
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值手续费开始,请求参数:{}", JSON.toJSONString(kunDebitSubRefundReq));
            Result<KunDebitSubRefundRsp> result = kCardKunAccountFacade.kunDebitSubRefund(kunDebitSubRefundReq);
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值手续费结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            // TODO 数据不存在的情况
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                    // 撤销完成和失败禁止撤销都算冲账成功
                    log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值手续费成功");
                    cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                } else {
                    log.error("[卡充值记账冲正通知事件]调用KUN账户冲账充值手续费失败");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[卡充值记账冲正通知事件]调用KUN账户冲账充值手续费状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }

        if (cardRechargeDetail.getDeductAcceptanceFeeBookkeepStatus() != null
                && (cardRechargeDetail.getDeductAcceptanceFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getDeductAcceptanceFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            // 调用kun进行冲账
            KunDebitSubRefundReq kunDebitSubRefundReq = this.assBaseKunDebitSubRefundReq(mpcToken, mpcGroupCode, cardRechargeDetail.getOrganizationNo());
            kunDebitSubRefundReq.setSourceRequestNo(cardRechargeDetail.getDeductAcceptanceFeeBookkeepRequestNo());
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值承兑费开始,请求参数:{}", JSON.toJSONString(kunDebitSubRefundReq));
            Result<KunDebitSubRefundRsp> result = kCardKunAccountFacade.kunDebitSubRefund(kunDebitSubRefundReq);
            log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值承兑费结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            // TODO 数据不存在的情况
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                    // 撤销完成和失败禁止撤销都算冲账成功
                    log.info("[卡充值记账冲正通知事件]调用KUN账户冲账充值承兑费成功");
                    cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                } else {
                    log.error("[卡充值记账冲正通知事件]调用KUN账户冲账充值承兑费失败");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[卡充值记账冲正通知事件]调用KUN账户冲账充值承兑费状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }

        if (cardRechargeDetail.getRechargeBookkeepStatus() != null
                && (cardRechargeDetail.getRechargeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getRechargeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            AccountReversalReq accountReversalReq = this.assAccountReversalReq(cardRechargeDetail);
            log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账开始,请求参数:{}", JSON.toJSONString(accountReversalReq));
            Result<Void> accountChangeBalanceResResult = accountTransactionFacade.reversal(accountReversalReq);
            log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
            if (Result.isSuccess(accountChangeBalanceResResult)) {
                // 有明确成功状态
                log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账成功");
                cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            } else {
                log.error("[卡充值记账冲正通知事件]调用账户服务给客户冲账失败");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("[卡充值记账冲正通知事件]扣账币种为数币的账户冲账完成");
    }

    /**
     * 处理扣账币种为数币的账户冲账
     *
     * @param cardRechargeDetail
     */
    public void processingFiatCurrencyBookkeepReversal(CardRechargeDetail cardRechargeDetail, String mpcToken, String mpcGroupCode) {
        log.info("[卡充值记账冲正通知事件]开始处理扣账币种为法币的账户冲账");
        // 已记账或者是状态未知的需要进行冲账
        if (cardRechargeDetail.getDeductPrincipalBookkeepStatus() != null
                && (cardRechargeDetail.getDeductPrincipalBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getDeductPrincipalBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            // 调用payX进行冲账
            PayXDebitSubRefundReq payXDebitSubRefundReq = this.assBasePayXDebitSubRefundReq(mpcToken, mpcGroupCode, cardRechargeDetail.getOrganizationNo());
            payXDebitSubRefundReq.setSourceRequestNo(cardRechargeDetail.getDeductPrincipalBookkeepRequestNo());
            log.info("[卡充值记账冲正通知事件]调用PayX账户冲账本金开始,请求参数:{}", JSON.toJSONString(payXDebitSubRefundReq));
            Result<PayXDebitSubRefundRsp> result = kCardPayXAccountFacade.payXDebitSubRefund(payXDebitSubRefundReq);
            log.info("[卡充值记账冲正通知事件]调用PayX账户冲账本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            // TODO 数据不存在的情况
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                    // 撤销完成和失败禁止撤销都算冲账成功
                    log.info("[卡充值记账冲正通知事件]调用PayX账户冲账本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                } else {
                    log.error("[卡充值记账冲正通知事件]调用PayX账户冲账本金失败");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[卡充值记账冲正通知事件]调用PayX账户冲账本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }

        if (cardRechargeDetail.getDeductRechargeFeeBookkeepStatus() != null
                && (cardRechargeDetail.getDeductRechargeFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getDeductRechargeFeeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            // 调用payX进行冲账
            PayXDebitSubRefundReq payXDebitSubRefundReq = this.assBasePayXDebitSubRefundReq(mpcToken, mpcGroupCode, cardRechargeDetail.getOrganizationNo());
            payXDebitSubRefundReq.setSourceRequestNo(cardRechargeDetail.getDeductRechargeFeeBookkeepRequestNo());
            log.info("[卡充值记账冲正通知事件]调用PayX账户冲账充值手续费开始,请求参数:{}", JSON.toJSONString(payXDebitSubRefundReq));
            Result<PayXDebitSubRefundRsp> result = kCardPayXAccountFacade.payXDebitSubRefund(payXDebitSubRefundReq);
            log.info("[卡充值记账冲正通知事件]调用PayX账户冲账充值手续费结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            // TODO 数据不存在的情况
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus())
                        || StringUtils.equals(result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                    // 撤销完成和失败禁止撤销都算冲账成功
                    log.info("[卡充值记账冲正通知事件]调用PayX账户冲账充值手续费成功");
                    cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                } else {
                    log.error("[卡充值记账冲正通知事件]调用PayX账户冲账充值手续费失败");
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.error("[卡充值记账冲正通知事件]调用PayX账户冲账充值手续费状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }

        if (cardRechargeDetail.getRechargeBookkeepStatus() != null
                && (cardRechargeDetail.getRechargeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.BOOKED.getValue()
                || cardRechargeDetail.getRechargeBookkeepStatus().intValue() == CardRechargeBookkeepStatusEnum.UNKNOW.getValue())) {
            AccountReversalReq accountReversalReq = this.assAccountReversalReq(cardRechargeDetail);
            log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账开始,请求参数:{}", JSON.toJSONString(accountReversalReq));
            Result<Void> accountChangeBalanceResResult = accountTransactionFacade.reversal(accountReversalReq);
            log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
            if (Result.isSuccess(accountChangeBalanceResResult)) {
                // 有明确成功状态
                log.info("[卡充值记账冲正通知事件]调用账户服务给客户冲账成功");
                cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            } else {
                log.error("[卡充值记账冲正通知事件]调用账户服务给客户冲账失败");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("[卡充值记账冲正通知事件]扣账币种为法币的账户冲账完成");
    }

    /**
     * 保存创建钱包操作记录
     *
     * @param cardRechargeReq
     */
    private CardRechargeDetail initCardRechargeDetail(CardRechargeReq cardRechargeReq, String deductCurrencyCode) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        CardRechargeDetail cardRechargeDetail = new CardRechargeDetail();
        cardRechargeDetail.setOrganizationNo(cardRechargeReq.getOrganizationNo());
        cardRechargeDetail.setBusinessType(CardRechargeBusinessTypeEnum.CARD_RECHARGE.getValue());
        cardRechargeDetail.setCustomerId(cardRechargeReq.getCustomerId());
        cardRechargeDetail.setRequestNo(String.valueOf(IdWorker.getId()));
        cardRechargeDetail.setCardId(cardRechargeReq.getCardId());
        cardRechargeDetail.setRechargeDatetime(now);
        cardRechargeDetail.setRechargeAmount(cardRechargeReq.getRechargeAmount());
        cardRechargeDetail.setRechargeCurrencyCode(cardRechargeReq.getRechargeCurrencyCode());
        cardRechargeDetail.setDeductCurrencyCode(deductCurrencyCode);
        // 充值币种只有美金和港币,精度先写死2
        cardRechargeDetail.setRechargeCurrencyPrecision(2);
        cardRechargeDetail.setDeductCurrencyPrecision(OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(deductCurrencyCode));
        // 记账状态全部初始化为未记账
        cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setDeductAcceptanceFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setDeductRechargeFeeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setRechargeStatus(OperationStatusEnum.PENDING.getStatus());
        cardRechargeDetail.setBookkeepReversalCount(0);
        cardRechargeDetail.setCreateTime(now);
        cardRechargeDetail.setLastModifyTime(now);
        int row = cardRechargeDetailMapper.insert(cardRechargeDetail);
        log.info("insert {} row CardRechargeDetail", row);
        return cardRechargeDetail;
    }

    /**
     * 组装kun询价请求参数
     *
     * @param cardRechargeDetail
     * @param organizationBasicInfo
     * @return
     */
    private KunAskPriceReq assKunAskPriceReq(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo) {
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(cardRechargeDetail.getRechargeAmount());
        kunAskPriceReq.setSideType(KunSideTypeEnum.BUY.getType());
        // kun接口币对必须数币在前法币在后
        kunAskPriceReq.setSymbol(cardRechargeDetail.getDeductCurrencyCode() + "_" + cardRechargeDetail.getRechargeCurrencyCode());
        return kunAskPriceReq;
    }


    /**
     * 组装payX询价请求参数
     *
     * @param cardRechargeDetail
     * @param organizationBasicInfo
     * @return
     */
    private PayXAskPriceReq assPayXAskPriceReq(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo) {
        PayXAskPriceReq payXAskPriceReq = new PayXAskPriceReq();
        payXAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        payXAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXAskPriceReq.setSourceCurrency(cardRechargeDetail.getRechargeCurrencyCode());
        payXAskPriceReq.setTargetCurrency(cardRechargeDetail.getDeductCurrencyCode());
        return payXAskPriceReq;
    }

    /**
     * 组装kun账户动账基础请求参数
     *
     * @param organizationBasicInfo
     * @return
     */
    private KunDebitSubReq assBaseKunDebitSubReq(OrganizationBasicInfo organizationBasicInfo) {
        KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
        kunDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        kunDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        return kunDebitSubReq;
    }

    /**
     * 组装kun账户冲账基础请求参数
     *
     * @param mpcGroupCode
     * @param organizationNo
     * @return
     */
    private KunDebitSubRefundReq assBaseKunDebitSubRefundReq(String mpcToken, String mpcGroupCode, String organizationNo) {
        KunDebitSubRefundReq kunDebitSubRefundReq = new KunDebitSubRefundReq();
        kunDebitSubRefundReq.setToken(mpcToken);
        kunDebitSubRefundReq.setGroupProductCode(mpcGroupCode);
        kunDebitSubRefundReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubRefundReq.setAccountNo(organizationNo);
        return kunDebitSubRefundReq;
    }

    /**
     * 组装PayX账户动账基础请求参数
     *
     * @param organizationBasicInfo
     * @return
     */
    private PayXDebitSubReq assBasePayXDebitSubReq(OrganizationBasicInfo organizationBasicInfo) {
        PayXDebitSubReq payXDebitSubReq = new PayXDebitSubReq();
        payXDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        payXDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        return payXDebitSubReq;
    }

    /**
     * 组装PayX账户冲账基础请求参数
     *
     * @param mpcGroupCode
     * @param organizationNo
     * @return
     */
    private PayXDebitSubRefundReq assBasePayXDebitSubRefundReq(String mpcToken, String mpcGroupCode, String organizationNo) {
        PayXDebitSubRefundReq payXDebitSubRefundReq = new PayXDebitSubRefundReq();
        payXDebitSubRefundReq.setToken(mpcToken);
        payXDebitSubRefundReq.setGroupProductCode(mpcGroupCode);
        payXDebitSubRefundReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXDebitSubRefundReq.setAccountNo(organizationNo);
        return payXDebitSubRefundReq;
    }

    /**
     * 组装账户服务动账请求参数
     *
     * @param cardRechargeDetail
     * @param accountNo
     * @return
     */
    private AccountChangeBalanceReq assAccountChangeBalanceReq(CardRechargeDetail cardRechargeDetail, String accountNo) {
        AccountChangeBalanceReq accountChangeBalanceReq = new AccountChangeBalanceReq();
        accountChangeBalanceReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        accountChangeBalanceReq.setRequestNo(String.valueOf(IdWorker.getId()));
        accountChangeBalanceReq.setBusinessOrganizationNo(cardRechargeDetail.getOrganizationNo());
        accountChangeBalanceReq.setAccountNo(accountNo);
        accountChangeBalanceReq.setBusinessType(BusinessTypeEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setBusinessAction(BusinessActionEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setAccountingAction(AccountingActionEnum.CREDIT.getValue());
        String businessTransactionNo = String.valueOf(IdWorker.getId());
        cardRechargeDetail.setRechargeBookkeepRequestNo(businessTransactionNo);
        accountChangeBalanceReq.setBusinessTransactionNo(businessTransactionNo);
        accountChangeBalanceReq.setAmount(cardRechargeDetail.getRechargeAmount());
        accountChangeBalanceReq.setCurrencyCode(cardRechargeDetail.getRechargeCurrencyCode());
        return accountChangeBalanceReq;
    }

    /**
     * 组装账户服务冲账请求参数
     *
     * @param cardRechargeDetail
     * @return
     */
    private AccountReversalReq assAccountReversalReq(CardRechargeDetail cardRechargeDetail) {
        AccountReversalReq accountReversalReq = new AccountReversalReq();
        accountReversalReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        accountReversalReq.setRequestNo(String.valueOf(IdWorker.getId()));
        accountReversalReq.setBusinessOrganizationNo(cardRechargeDetail.getOrganizationNo());
        accountReversalReq.setOriginalBusinessTransactionNo(cardRechargeDetail.getRechargeBookkeepRequestNo());
        return accountReversalReq;
    }

    /**
     * 发送卡充值账户冲账事件到mq
     *
     * @param cardRechargeDetail
     * @param mpcGroupCode
     */
    public void sendCardRechargeBookkeepReversalToMq(CardRechargeDetail cardRechargeDetail, String mpcToken, String mpcGroupCode) {
        log.info("发送卡充值账户冲账事件到mq");
        CardRechargeBookkeepReversalEventVO cardRechargeBookkeepReversalEventVO = new CardRechargeBookkeepReversalEventVO();
        cardRechargeBookkeepReversalEventVO.setCardRechargeDetailId(cardRechargeDetail.getId());
        cardRechargeBookkeepReversalEventVO.setRechargeDatetime(cardRechargeDetail.getRechargeDatetime());
        cardRechargeBookkeepReversalEventVO.setMpcToken(mpcToken);
        cardRechargeBookkeepReversalEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.CARD_RECHARGE_BOOKKEEP_REVERSAL_EVENT_TOPIC, cardRechargeBookkeepReversalEventVO, 10000, MqTopicConstant.DELAY_LEVEL_30S);
    }

    /**
     * 卡充值状态查询
     *
     * @param cardRechargeStatusQueryReq
     * @return
     */
    public Result<CardRechargeStatusQueryRes> cardRechargeStatusQuery(CardRechargeStatusQueryReq cardRechargeStatusQueryReq) {
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cardRechargeStatusQueryReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cardRechargeStatusQueryReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cardRechargeStatusQueryReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-卡充值状态查询]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cardRechargeStatusQueryReq.getOrganizationNo(), cardRechargeStatusQueryReq.getCustomerId(), cardRechargeStatusQueryReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        // 限制时间范围,因为是按季度分表的,直接限制60天
        LocalDateTime now = LocalDateTime.now();
        CardRechargeDetail cardRechargeDetail = cardRechargeDetailMapper.selectOne(Wrappers.<CardRechargeDetail>lambdaQuery()
                .eq(CardRechargeDetail::getOrganizationNo, cardRechargeStatusQueryReq.getOrganizationNo())
                .eq(CardRechargeDetail::getCustomerId, cardRechargeStatusQueryReq.getCustomerId())
                .eq(CardRechargeDetail::getCardId, cardRechargeStatusQueryReq.getCardId())
                .eq(CardRechargeDetail::getRequestNo, cardRechargeStatusQueryReq.getOriginalRequestNo())
                .between(CardRechargeDetail::getRechargeDatetime, now.minusDays(60), now));
        if (cardRechargeDetail == null) {
            log.error("[卡片管理-卡充值状态查询]卡充值记录不存在,机构号:{},客户号:{},卡id:{},原请求流水号:{}",
                    cardRechargeStatusQueryReq.getOrganizationNo(), cardRechargeStatusQueryReq.getCustomerId(), cardRechargeStatusQueryReq.getCardId(), cardRechargeStatusQueryReq.getOriginalRequestNo());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        CardRechargeStatusQueryRes cardRechargeStatusQueryRes = new CardRechargeStatusQueryRes();
        cardRechargeStatusQueryRes.setRechargeStatus(cardRechargeDetail.getRechargeStatus());
        cardRechargeStatusQueryRes.setCardId(cardRechargeDetail.getCardId());
        cardRechargeStatusQueryRes.setRechargeAmount(cardRechargeDetail.getRechargeAmount());
        cardRechargeStatusQueryRes.setRechargeCurrencyCode(cardRechargeDetail.getRechargeCurrencyCode());
        cardRechargeStatusQueryRes.setDeductAmount(cardRechargeDetail.getDeductTotalAmount());
        cardRechargeStatusQueryRes.setDeductCurrencyCode(cardRechargeDetail.getDeductCurrencyCode());
        return Result.success(cardRechargeStatusQueryRes);
    }

    /**
     * 分页查询卡充值记录
     *
     * @param req
     * @return
     */
    public PageResult<PageQueryCardRechargeDetailRes> pageQueryCardRechargeDetail(
            PageQueryCardRechargeDetailReq req) {
        if (req.getEndDate().isBefore(LocalDate.of(2025, 5, 1))) {
            log.warn("[分页查询卡充值记录]结束时间不能早于2025-05-01,直接返回空集合");
            return new PageResult<>(Collections.emptyList(), req.getPageNum(), req.getPageSize(), 0);
        }
        LocalDate now = LocalDate.now();
        if (req.getEndDate().isAfter(now)) {
            log.warn("[分页查询卡充值记录]结束时间不能超过当前,重置结束时间为当前时间");
            req.setEndDate(now);
        }
        long days = ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate());
        if (days > 365) {
            log.error("[分页查询卡充值记录]查询时间跨度不能超过365天");
            throw new BusinessException(CommonTipConstant.QUERY_TIME_SPAN_CANNOT_EXCEED_365_DAYS);
        }
        // 查询是否存在用户已激活的卡信息,不存在则一定没有充值记录
        LambdaQueryWrapper<OrganizationCustomerCardInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(OrganizationCustomerCardInfo::getOrganizationNo, req.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, req.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardActiveStatus, CardActiveStatusEnum.ACTIVATED.getStatus());
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getCardId())) {
            wrapper.eq(OrganizationCustomerCardInfo::getCardId, req.getCardId());
        }
        long count = organizationCustomerCardInfoMapper.selectCount(wrapper);
        if (count == 0) {
            log.warn("[分页查询卡充值记录]用户不存在已激活的卡, 直接返回空集合");
            return new PageResult<>(Collections.emptyList(), req.getPageNum(), req.getPageSize(), 0);
        }
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(req.getPageNum());
        pageParam.setPageSize(req.getPageSize());
        return PageHelperUtil.getPage(pageParam, () -> cardRechargeDetailExtMapper.listCardRechargeDetailByWhere(
                req.getOrganizationNo(), req.getCustomerId(), req.getCardId(),
                req.getStartDate().atTime(0, 0, 0),
                req.getEndDate().atTime(23, 59, 59)));
    }
}
